package models

import (
	"database/sql"
	"panel/config"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// User Struct <PERSON><PERSON><PERSON>
type User struct {
	ID        int       `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Password  string    `json:"-"` // Không Trả Về Password Trong JSON
	FullName  string    `json:"full_name"`
	Role      string    `json:"role"`
	GoogleID  string    `json:"google_id"`
	Avatar    string    `json:"avatar"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CreateUser Tạo Người Dùng Mới
func CreateUser(user *User) error {
	// Hash Password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	query := `
		INSERT INTO users (username, email, password, full_name, role, google_id, avatar)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	result, err := config.DB.Exec(query, user.Username, user.Email, string(hashedPassword), 
		user.FullName, user.Role, user.GoogleID, user.Avatar)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	user.ID = int(id)
	return nil
}

// GetUserByEmail Lấy Người Dùng Theo Email
func GetUserByEmail(email string) (*User, error) {
	user := &User{}
	query := `
		SELECT id, username, email, password, full_name, role, google_id, avatar, created_at, updated_at
		FROM users WHERE email = ?
	`

	err := config.DB.QueryRow(query, email).Scan(
		&user.ID, &user.Username, &user.Email, &user.Password,
		&user.FullName, &user.Role, &user.GoogleID, &user.Avatar,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return user, nil
}

// GetUserByID Lấy Người Dùng Theo ID
func GetUserByID(id int) (*User, error) {
	user := &User{}
	query := `
		SELECT id, username, email, password, full_name, role, google_id, avatar, created_at, updated_at
		FROM users WHERE id = ?
	`

	err := config.DB.QueryRow(query, id).Scan(
		&user.ID, &user.Username, &user.Email, &user.Password,
		&user.FullName, &user.Role, &user.GoogleID, &user.Avatar,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return user, nil
}

// ValidatePassword Kiểm Tra Mật Khẩu
func (u *User) ValidatePassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
	return err == nil
}

// GetAllUsers Lấy Tất Cả Người Dùng (Cho Admin)
func GetAllUsers() ([]User, error) {
	query := `
		SELECT id, username, email, full_name, role, google_id, avatar, created_at, updated_at
		FROM users ORDER BY created_at DESC
	`

	rows, err := config.DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var users []User
	for rows.Next() {
		var user User
		err := rows.Scan(
			&user.ID, &user.Username, &user.Email,
			&user.FullName, &user.Role, &user.GoogleID, &user.Avatar,
			&user.CreatedAt, &user.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		users = append(users, user)
	}

	return users, nil
}
