{{define "content"}}
<div class="admin-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div class="header-content">
            <h1><i class="fas fa-tachometer-alt"></i> Dashboard Admin</h1>
            <p>Quản Lý Và Giám Sát Hệ Thống</p>
        </div>
        <div class="header-actions">
            <button class="btn btn-primary" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt"></i> Làm <PERSON>ớ<PERSON>
            </button>
            <button class="btn btn-secondary" onclick="exportReport()">
                <i class="fas fa-download"></i> Xuất Báo Cáo
            </button>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="stats-overview">
        <div class="stat-card">
            <div class="stat-icon users">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3 class="stat-number stat-total-users">{{len .users}}</h3>
                <p class="stat-label">Tổng Người Dùng</p>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>+12% so với tháng trước</span>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon active">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="stat-content">
                <h3 class="stat-number stat-active-users">89</h3>
                <p class="stat-label">Người Dùng Hoạt Động</p>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>+5% so với hôm qua</span>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon new">
                <i class="fas fa-user-plus"></i>
            </div>
            <div class="stat-content">
                <h3 class="stat-number stat-new-users">25</h3>
                <p class="stat-label">Người Dùng Mới</p>
                <div class="stat-trend">
                    <i class="fas fa-arrow-down"></i>
                    <span>-2% so với tuần trước</span>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon admin">
                <i class="fas fa-crown"></i>
            </div>
            <div class="stat-content">
                <h3 class="stat-number stat-admin-users">5</h3>
                <p class="stat-label">Quản Trị Viên</p>
                <div class="stat-trend">
                    <i class="fas fa-minus"></i>
                    <span>Không thay đổi</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="dashboard-content">
        <!-- User Management -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-users-cog"></i> Quản Lý Người Dùng</h3>
                <div class="card-actions">
                    <button class="btn btn-primary btn-sm" onclick="addNewUser()">
                        <i class="fas fa-plus"></i> Thêm Người Dùng
                    </button>
                </div>
            </div>
            
            <!-- Search and Filter -->
            <div class="table-controls">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Tìm kiếm người dùng..." id="userSearch">
                </div>
                <div class="filter-controls">
                    <select id="roleFilter">
                        <option value="">Tất cả vai trò</option>
                        <option value="admin">Admin</option>
                        <option value="user">User</option>
                    </select>
                    <select id="statusFilter">
                        <option value="">Tất cả trạng thái</option>
                        <option value="active">Hoạt động</option>
                        <option value="inactive">Không hoạt động</option>
                    </select>
                </div>
            </div>

            <!-- Users Table -->
            <div class="table-container">
                <table class="table users-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll">
                            </th>
                            <th>Người Dùng</th>
                            <th>Email</th>
                            <th>Vai Trò</th>
                            <th>Trạng Thái</th>
                            <th>Ngày Tham Gia</th>
                            <th>Thao Tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{range .users}}
                        <tr>
                            <td>
                                <input type="checkbox" class="user-checkbox" value="{{.ID}}">
                            </td>
                            <td>
                                <div class="user-info">
                                    <img src="{{if .Avatar}}{{.Avatar}}{{else}}/static/images/default-avatar.png{{end}}" 
                                         alt="Avatar" class="user-avatar">
                                    <div class="user-details">
                                        <strong>{{.FullName}}</strong>
                                        <span>@{{.Username}}</span>
                                    </div>
                                </div>
                            </td>
                            <td>{{.Email}}</td>
                            <td>
                                <span class="role-badge {{.Role}}">
                                    {{if eq .Role "admin"}}
                                        <i class="fas fa-crown"></i> Admin
                                    {{else}}
                                        <i class="fas fa-user"></i> User
                                    {{end}}
                                </span>
                            </td>
                            <td>
                                <span class="status-badge active">
                                    <i class="fas fa-circle"></i> Hoạt động
                                </span>
                            </td>
                            <td>{{.CreatedAt.Format "02/01/2006"}}</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-sm btn-icon btn-edit-user" 
                                            data-user-id="{{.ID}}" 
                                            title="Chỉnh sửa">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-icon btn-view-user" 
                                            data-user-id="{{.ID}}" 
                                            title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {{if ne .Role "admin"}}
                                    <button class="btn btn-sm btn-icon btn-delete-user" 
                                            data-user-id="{{.ID}}" 
                                            title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {{end}}
                                </div>
                            </td>
                        </tr>
                        {{end}}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <div class="pagination-info">
                    Hiển thị 1-10 của {{len .users}} người dùng
                </div>
                <div class="pagination">
                    <button class="btn btn-sm" disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="btn btn-sm active">1</button>
                    <button class="btn btn-sm">2</button>
                    <button class="btn btn-sm">3</button>
                    <button class="btn btn-sm">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- System Analytics -->
        <div class="analytics-grid">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-chart-line"></i> Thống Kê Truy Cập</h3>
                </div>
                <div class="chart-container">
                    <canvas id="accessChart"></canvas>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-chart-pie"></i> Phân Bố Người Dùng</h3>
                </div>
                <div class="chart-container">
                    <canvas id="userDistributionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-history"></i> Hoạt Động Gần Đây</h3>
                <a href="/admin/activities" class="view-all">Xem Tất Cả</a>
            </div>
            <div class="activity-timeline">
                <div class="activity-item">
                    <div class="activity-icon user-add">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Người dùng mới</strong> đã đăng ký tài khoản</p>
                        <span class="activity-time">5 phút trước</span>
                        <span class="activity-user"><EMAIL></span>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon login">
                        <i class="fas fa-sign-in-alt"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Đăng nhập</strong> từ địa chỉ IP mới</p>
                        <span class="activity-time">15 phút trước</span>
                        <span class="activity-user"><EMAIL></span>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon settings">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Cài đặt hệ thống</strong> đã được cập nhật</p>
                        <span class="activity-time">1 giờ trước</span>
                        <span class="activity-user">Hệ thống</span>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon security">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Kiểm tra bảo mật</strong> đã hoàn thành</p>
                        <span class="activity-time">2 giờ trước</span>
                        <span class="activity-user">Hệ thống</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.admin-dashboard {
    padding: 2rem 0;
}

.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.header-content p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-icon.users { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.active { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
.stat-icon.new { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }
.stat-icon.admin { background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); }

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    margin: 0;
    color: #333;
}

.stat-label {
    color: #666;
    margin: 0.25rem 0;
    font-size: 0.9rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    color: #28a745;
}

.dashboard-content {
    display: grid;
    gap: 2rem;
}

.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.search-box {
    position: relative;
    width: 300px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-box input {
    width: 100%;
    padding: 0.5rem 0.5rem 0.5rem 2.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.filter-controls {
    display: flex;
    gap: 1rem;
}

.filter-controls select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.table-container {
    overflow-x: auto;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-details strong {
    display: block;
    font-weight: 600;
}

.user-details span {
    font-size: 0.8rem;
    color: #666;
}

.role-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.role-badge.admin {
    background: #fff3cd;
    color: #856404;
}

.role-badge.user {
    background: #d1ecf1;
    color: #0c5460;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 5px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-edit-user {
    background: #e3f2fd;
    color: #1976d2;
}

.btn-view-user {
    background: #f3e5f5;
    color: #7b1fa2;
}

.btn-delete-user {
    background: #ffebee;
    color: #d32f2f;
}

.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e9ecef;
}

.pagination {
    display: flex;
    gap: 0.25rem;
}

.pagination .btn.active {
    background: #667eea;
    color: white;
}

.analytics-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.chart-container {
    height: 300px;
    padding: 1rem;
}

.activity-timeline {
    padding: 1rem 1.5rem;
}

.activity-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.activity-icon.user-add { background: #28a745; }
.activity-icon.login { background: #17a2b8; }
.activity-icon.settings { background: #6c757d; }
.activity-icon.security { background: #dc3545; }

.activity-content p {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
}

.activity-time,
.activity-user {
    display: block;
    font-size: 0.8rem;
    color: #666;
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .stats-overview {
        grid-template-columns: 1fr;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .table-controls {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .search-box {
        width: 100%;
    }
}
</style>
{{end}}

{{define "scripts"}}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize Charts
const accessCtx = document.getElementById('accessChart').getContext('2d');
const accessChart = new Chart(accessCtx, {
    type: 'line',
    data: {
        labels: ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'],
        datasets: [{
            label: 'Lượt truy cập',
            data: [120, 190, 300, 500, 200, 300, 450],
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

const userDistCtx = document.getElementById('userDistributionChart').getContext('2d');
const userDistChart = new Chart(userDistCtx, {
    type: 'doughnut',
    data: {
        labels: ['Admin', 'User'],
        datasets: [{
            data: [5, {{len .users}}],
            backgroundColor: ['#dc3545', '#667eea'],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Dashboard Functions
function refreshDashboard() {
    location.reload();
}

function exportReport() {
    // Implement export functionality
    console.log('Exporting report...');
}

function addNewUser() {
    // Implement add user modal
    console.log('Adding new user...');
}

// User Management
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

// Search functionality
document.getElementById('userSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('.users-table tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// Filter functionality
document.getElementById('roleFilter').addEventListener('change', function() {
    filterUsers();
});

document.getElementById('statusFilter').addEventListener('change', function() {
    filterUsers();
});

function filterUsers() {
    const roleFilter = document.getElementById('roleFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const rows = document.querySelectorAll('.users-table tbody tr');
    
    rows.forEach(row => {
        let show = true;
        
        if (roleFilter) {
            const roleBadge = row.querySelector('.role-badge');
            if (!roleBadge.classList.contains(roleFilter)) {
                show = false;
            }
        }
        
        if (statusFilter) {
            const statusBadge = row.querySelector('.status-badge');
            if (!statusBadge.classList.contains(statusFilter)) {
                show = false;
            }
        }
        
        row.style.display = show ? '' : 'none';
    });
}
</script>
{{end}}
