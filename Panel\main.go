package main

import (
	"log"
	"panel/config"
	"panel/routes"

	"github.com/gin-gonic/gin"
)

func main() {
	// Khởi tạo kết nối database
	config.InitDatabase()

	// Tạo Gin router
	r := gin.Default()

	// Load HTML templates
	r.LoadHTMLGlob("views/**/*")

	// Serve static files
	r.St<PERSON>("/static", "./static")

	// Thiết lập routes
	routes.SetupRoutes(r)

	// Chạy server trên port 8080
	log.Println("Server đang chạy trên http://localhost:8080")
	r.Run(":8080")
}
