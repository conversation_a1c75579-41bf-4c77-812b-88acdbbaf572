package main

import (
	"log"
	"panel/config"
	"panel/routes"

	"github.com/gin-gonic/gin"
)

func main() {
	// Khởi Tạo Kết Nối Database
	config.InitDatabase()

	// Tạo Gin Router
	r := gin.Default()

	// Load HTML Templates
	r.LoadHTMLGlob("views/**/*")

	// Serve Static Files
	r.Static("/static", "./static")

	// Thiết Lập Routes
	routes.SetupRoutes(r)

	// Chạy Server Trên Port 8080
	log.Println("Server Đang Chạy Trên http://localhost:8080")
	r.Run(":8080")
}
