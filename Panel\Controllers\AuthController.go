package controllers

import (
	"net/http"
	"panel/models"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

// ShowLogin Hiển Thị Trang Đăng <PERSON>hập
func ShowLogin(c *gin.Context) {
	c.HTML(http.StatusOK, "auth/login.html", gin.H{
		"title": "Đăng <PERSON>ậ<PERSON>",
	})
}

// ShowRegister Hiển Thị Trang Đăng Ký
func ShowRegister(c *gin.Context) {
	c.HTML(http.StatusOK, "auth/register.html", gin.H{
		"title": "<PERSON>ăng <PERSON>",
	})
}

// ShowForgotPassword Hiển Thị Trang Quên M<PERSON>t <PERSON>
func ShowForgotPassword(c *gin.Context) {
	c.HTML(http.StatusOK, "auth/forgot_password.html", gin.H{
		"title": "Quên <PERSON>",
	})
}

// Login Xử Lý <PERSON>
func Login(c *gin.Context) {
	email := c.PostForm("email")
	password := c.<PERSON>Form("password")

	if email == "" || password == "" {
		c.HTML(http.StatusBadRequest, "auth/login.html", gin.H{
			"title": "Đăng Nhập",
			"error": "Vui Lòng Nhập Đầy Đủ Thông Tin",
		})
		return
	}

	// Tìm User Theo Email
	user, err := models.GetUserByEmail(email)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "auth/login.html", gin.H{
			"title": "Đăng Nhập",
			"error": "Lỗi Hệ Thống, Vui Lòng Thử Lại",
		})
		return
	}

	if user == nil || !user.ValidatePassword(password) {
		c.HTML(http.StatusBadRequest, "auth/login.html", gin.H{
			"title": "Đăng Nhập",
			"error": "Email Hoặc Mật Khẩu Không Đúng",
		})
		return
	}

	// Lưu Session
	session := sessions.Default(c)
	session.Set("user_id", user.ID)
	session.Save()

	c.Redirect(http.StatusFound, "/")
}

// Register Xử Lý Đăng Ký
func Register(c *gin.Context) {
	username := c.PostForm("username")
	email := c.PostForm("email")
	password := c.PostForm("password")
	confirmPassword := c.PostForm("confirm_password")
	fullName := c.PostForm("full_name")

	// Validate Input
	if username == "" || email == "" || password == "" || fullName == "" {
		c.HTML(http.StatusBadRequest, "auth/register.html", gin.H{
			"title": "Đăng Ký",
			"error": "Vui Lòng Nhập Đầy Đủ Thông Tin",
		})
		return
	}

	if password != confirmPassword {
		c.HTML(http.StatusBadRequest, "auth/register.html", gin.H{
			"title": "Đăng Ký",
			"error": "Mật Khẩu Xác Nhận Không Khớp",
		})
		return
	}

	// Kiểm Tra Email Đã Tồn Tại
	existingUser, err := models.GetUserByEmail(email)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "auth/register.html", gin.H{
			"title": "Đăng Ký",
			"error": "Lỗi Hệ Thống, Vui Lòng Thử Lại",
		})
		return
	}

	if existingUser != nil {
		c.HTML(http.StatusBadRequest, "auth/register.html", gin.H{
			"title": "Đăng Ký",
			"error": "Email Đã Được Sử Dụng",
		})
		return
	}

	// Tạo User Mới
	user := &models.User{
		Username: username,
		Email:    email,
		Password: password,
		FullName: fullName,
		Role:     "user",
	}

	err = models.CreateUser(user)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "auth/register.html", gin.H{
			"title": "Đăng Ký",
			"error": "Không Thể Tạo Tài Khoản, Vui Lòng Thử Lại",
		})
		return
	}

	// Tự Động Đăng Nhập Sau Khi Đăng Ký
	session := sessions.Default(c)
	session.Set("user_id", user.ID)
	session.Save()

	c.Redirect(http.StatusFound, "/")
}

// Logout Xử Lý Đăng Xuất
func Logout(c *gin.Context) {
	session := sessions.Default(c)
	session.Delete("user_id")
	session.Save()

	c.Redirect(http.StatusFound, "/auth/login")
}
