package routes

import (
	"panel/controllers"
	"panel/middleware"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
)

// SetupRoutes Thiết lập tất cả routes
func SetupRoutes(r *gin.Engine) {
	// Thiết lập session
	store := cookie.NewStore([]byte("secret-key-panel-app"))
	r.Use(sessions.Sessions("panel-session", store))

	// Routes công khai (không cần đăng nhập)
	public := r.Group("/")
	{
		// Redirect root to home
		public.GET("/", func(c *gin.Context) {
			c.Redirect(302, "/home")
		})
	}

	// Auth routes (chỉ cho khách chưa đăng nhập)
	auth := r.Group("/auth")
	auth.Use(middleware.GuestOnly())
	{
		auth.GET("/login", controllers.ShowLogin)
		auth.POST("/login", controllers.Login)
		auth.GET("/register", controllers.ShowRegister)
		auth.POST("/register", controllers.Register)
		auth.GET("/forgot-password", controllers.ShowForgotPassword)
	}

	// Logout route (cho người đã đăng nhập)
	r.POST("/auth/logout", middleware.AuthRequired(), controllers.Logout)

	// Home routes (cần đăng nhập)
	home := r.Group("/home")
	home.Use(middleware.AuthRequired())
	{
		home.GET("/", controllers.ShowHome)
		home.GET("/profile", controllers.ShowProfile)
		home.POST("/profile", controllers.UpdateProfile)
	}

	// Chat routes (cần đăng nhập)
	chat := r.Group("/chat")
	chat.Use(middleware.AuthRequired())
	{
		chat.GET("/", controllers.ShowChat)
	}

	// Admin routes (cần đăng nhập và quyền admin)
	admin := r.Group("/admin")
	admin.Use(middleware.AuthRequired())
	admin.Use(middleware.AdminRequired())
	{
		admin.GET("/dashboard", controllers.ShowAdminDashboard)
	}

	// API routes
	api := r.Group("/api")
	api.Use(middleware.AuthRequired())
	{
		api.GET("/user", controllers.GetUserAPI)
		
		// Admin API
		adminAPI := api.Group("/admin")
		adminAPI.Use(middleware.AdminRequired())
		{
			adminAPI.GET("/users", controllers.GetAllUsersAPI)
		}
	}
}
