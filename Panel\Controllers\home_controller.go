package controllers

import (
	"net/http"
	"panel/models"

	"github.com/gin-gonic/gin"
)

// ShowHome hiển thị trang chủ
func ShowHome(c *gin.Context) {
	user, _ := c.Get("user")
	
	c.HTML(http.StatusOK, "home/index.html", gin.H{
		"title": "Trang chủ",
		"user":  user,
	})
}

// ShowProfile hiển thị trang thông tin cá nhân
func ShowProfile(c *gin.Context) {
	user, _ := c.Get("user")
	
	c.HTML(http.StatusOK, "home/profile.html", gin.H{
		"title": "Thông tin cá nhân",
		"user":  user,
	})
}

// UpdateProfile cập nhật thông tin cá nhân
func UpdateProfile(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.Redirect(http.StatusFound, "/auth/login")
		return
	}

	currentUser := user.(*models.User)
	
	// L<PERSON><PERSON> dữ liệu từ form
	fullName := c.PostForm("full_name")
	username := c.PostForm("username")

	if fullName == "" || username == "" {
		c.HTML(http.StatusBadRequest, "home/profile.html", gin.H{
			"title": "Thông tin cá nhân",
			"user":  currentUser,
			"error": "Vui lòng nhập đầy đủ thông tin",
		})
		return
	}

	// Cập nhật thông tin (đây là ví dụ đơn giản, trong thực tế cần thêm method UpdateUser)
	c.HTML(http.StatusOK, "home/profile.html", gin.H{
		"title":   "Thông tin cá nhân",
		"user":    currentUser,
		"success": "Cập nhật thông tin thành công",
	})
}

// ShowChat hiển thị trang chat
func ShowChat(c *gin.Context) {
	user, _ := c.Get("user")
	
	c.HTML(http.StatusOK, "chat/chat.html", gin.H{
		"title": "Chat",
		"user":  user,
	})
}
