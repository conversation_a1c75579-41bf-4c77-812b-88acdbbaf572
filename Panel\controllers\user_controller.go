package controllers

import (
	"net/http"
	"panel/models"

	"github.com/gin-gonic/gin"
)

// ShowAdminDashboard Hiển thị dashboard admin
func ShowAdminDashboard(c *gin.Context) {
	users, err := models.GetAllUsers()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "admin/dashboard.html", gin.H{
			"title": "Dashboard Admin",
			"error": "Không Thể <PERSON>ch <PERSON>ờ<PERSON>ùng",
		})
		return
	}

	user, _ := c.Get("user")
	
	c.HTML(http.StatusOK, "admin/dashboard.html", gin.H{
		"title": "Dashboard Admin",
		"user":  user,
		"users": users,
	})
}

// GetUserAPI API lấy thông tin người dùng (JSON)
func GetUserAPI(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSO<PERSON>(http.StatusUnauthorized, gin.H{
			"error": "<PERSON><PERSON><PERSON>",
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"user": user,
	})
}

// GetAllUsersAPI API lấy tất cả người dùng (chỉ admin)
func GetAllUsersAPI(c *gin.Context) {
	users, err := models.GetAllUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Không Thể Tải Danh Sách Người Dùng",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"users": users,
	})
}
