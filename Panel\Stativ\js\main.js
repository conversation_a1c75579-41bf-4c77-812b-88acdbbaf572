// Main JavaScript File For Panel Application

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    console.log('Panel Application Loaded');

    // Initialize Application
    initializeApp();
});

// Export Functions For Use In Other Scripts
window.PanelApp = {
    showAlert,
    showLoading,
    hideLoading,
    apiRequest,
    validateForm,
    escapeHtml
};

// Initialize Application
function initializeApp() {
    // Setup Form Validations
    setupFormValidations();
    
    // Setup Navigation
    setupNavigation();
    
    // Setup Chat If Present
    if (document.querySelector('.chat-container')) {
        initializeChat();
    }
    
    // Setup Admin Dashboard If Present
    if (document.querySelector('.admin-dashboard')) {
        initializeAdminDashboard();
    }
    
    // Setup Profile Page If Present
    if (document.querySelector('.profile-page')) {
        initializeProfilePage();
    }
}

// Form Validations
function setupFormValidations() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                return false;
            }
        });
        
        // Real-time validation
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
    });
}

// Validate Form
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    // Password confirmation validation
    const password = form.querySelector('input[name="password"]');
    const confirmPassword = form.querySelector('input[name="confirm_password"]');
    
    if (password && confirmPassword) {
        if (password.value !== confirmPassword.value) {
            showFieldError(confirmPassword, 'Mật Khẩu Xác Nhận Không Khớp');
            isValid = false;
        } else {
            clearFieldError(confirmPassword);
        }
    }
    
    return isValid;
}

// Validate Individual Field
function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.getAttribute('name');
    
    // Clear previous errors
    clearFieldError(field);
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'Trường Này Là Bắt Buộc');
        return false;
    }
    
    // Email validation
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showFieldError(field, 'Email Không Hợp Lệ');
            return false;
        }
    }
    
    // Password validation
    if (fieldName === 'password' && value) {
        if (value.length < 6) {
            showFieldError(field, 'Mật Khẩu Phải Có Ít Nhất 6 Ký Tự');
            return false;
        }
    }
    
    return true;
}

// Show Field Error
function showFieldError(field, message) {
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.style.color = '#dc3545';
    errorElement.style.fontSize = '0.875rem';
    errorElement.style.marginTop = '0.25rem';
    errorElement.textContent = message;
    
    // Remove existing error
    clearFieldError(field);
    
    // Add new error
    field.parentNode.appendChild(errorElement);
    field.style.borderColor = '#dc3545';
}

// Clear Field Error
function clearFieldError(field) {
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    field.style.borderColor = '#ddd';
}

// Setup Navigation
function setupNavigation() {
    // Mobile menu toggle
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');
    
    if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.addEventListener('click', function() {
            navLinks.classList.toggle('active');
        });
    }
    
    // Active navigation highlighting
    const currentPath = window.location.pathname;
    const navItems = document.querySelectorAll('.nav-links a');
    
    navItems.forEach(item => {
        if (item.getAttribute('href') === currentPath) {
            item.classList.add('active');
        }
    });
}

// Initialize Chat
function initializeChat() {
    const chatInput = document.querySelector('.chat-input');
    const chatMessages = document.querySelector('.chat-messages');
    const sendBtn = document.querySelector('.chat-send-btn');
    
    if (chatInput && sendBtn) {
        // Send message on button click
        sendBtn.addEventListener('click', sendMessage);
        
        // Send message on Enter key
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    }
    
    // Auto-scroll to bottom
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// Send Chat Message
function sendMessage() {
    const chatInput = document.querySelector('.chat-input');
    const chatMessages = document.querySelector('.chat-messages');
    const message = chatInput.value.trim();
    
    if (!message) return;
    
    // Add message to chat
    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message user-message';
    messageElement.innerHTML = `
        <div class="message-content">${escapeHtml(message)}</div>
        <div class="message-time">${new Date().toLocaleTimeString()}</div>
    `;
    
    chatMessages.appendChild(messageElement);
    chatInput.value = '';
    
    // Auto-scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // Here you would typically send the message to the server
    // For now, we'll just simulate a response
    setTimeout(() => {
        simulateResponse();
    }, 1000);
}

// Simulate Chat Response
function simulateResponse() {
    const chatMessages = document.querySelector('.chat-messages');
    const responses = [
        'Xin Chào! Tôi Có Thể Giúp Gì Cho Bạn?',
        'Cảm Ơn Bạn Đã Liên Hệ!',
        'Tôi Đang Xử Lý Yêu Cầu Của Bạn...',
        'Bạn Có Cần Hỗ Trợ Thêm Không?'
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message bot-message';
    messageElement.innerHTML = `
        <div class="message-content">${randomResponse}</div>
        <div class="message-time">${new Date().toLocaleTimeString()}</div>
    `;
    
    chatMessages.appendChild(messageElement);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Initialize Admin Dashboard
function initializeAdminDashboard() {
    // Load user statistics
    loadUserStats();
    
    // Setup user management
    setupUserManagement();
}

// Load User Statistics
function loadUserStats() {
    // This would typically fetch from API
    // For now, we'll use placeholder data
    const stats = {
        totalUsers: 150,
        activeUsers: 120,
        newUsers: 25,
        adminUsers: 5
    };
    
    updateStatsDisplay(stats);
}

// Update Statistics Display
function updateStatsDisplay(stats) {
    const statsElements = {
        totalUsers: document.querySelector('.stat-total-users'),
        activeUsers: document.querySelector('.stat-active-users'),
        newUsers: document.querySelector('.stat-new-users'),
        adminUsers: document.querySelector('.stat-admin-users')
    };
    
    Object.keys(statsElements).forEach(key => {
        if (statsElements[key]) {
            statsElements[key].textContent = stats[key];
        }
    });
}

// Setup User Management
function setupUserManagement() {
    const userTable = document.querySelector('.users-table');
    if (userTable) {
        // Add event listeners for user actions
        userTable.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-edit-user')) {
                const userId = e.target.dataset.userId;
                editUser(userId);
            } else if (e.target.classList.contains('btn-delete-user')) {
                const userId = e.target.dataset.userId;
                deleteUser(userId);
            }
        });
    }
}

// Initialize Profile Page
function initializeProfilePage() {
    // Avatar upload
    const avatarInput = document.querySelector('#avatar-input');
    const avatarPreview = document.querySelector('.profile-avatar');
    
    if (avatarInput && avatarPreview) {
        avatarInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    avatarPreview.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

// Utility Functions
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showAlert(message, type = 'info') {
    const alertElement = document.createElement('div');
    alertElement.className = `alert alert-${type}`;
    alertElement.textContent = message;
    
    const container = document.querySelector('.main-content') || document.body;
    container.insertBefore(alertElement, container.firstChild);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        alertElement.remove();
    }, 5000);
}

function showLoading() {
    const spinner = document.createElement('div');
    spinner.className = 'spinner';
    spinner.id = 'loading-spinner';
    
    const container = document.querySelector('.main-content') || document.body;
    container.appendChild(spinner);
}

function hideLoading() {
    const spinner = document.querySelector('#loading-spinner');
    if (spinner) {
        spinner.remove();
    }
}

// API Helper Functions
async function apiRequest(url, options = {}) {
    try {
        showLoading();
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Có Lỗi Xảy Ra');
        }
        
        return data;
    } catch (error) {
        showAlert(error.message, 'error');
        throw error;
    } finally {
        hideLoading();
    }
}

// Export functions for use in other scripts
window.PanelApp = {
    showAlert,
    showLoading,
    hideLoading,
    apiRequest,
    validateForm,
    escapeHtml
};
