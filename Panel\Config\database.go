package config

import (
	"log"
)

// Mock database for demo purposes
var MockUsers = make(map[int]map[string]interface{})
var UserIDCounter = 1

// InitDatabase Khởi Tạo Mock Database
func InitDatabase() {
	log.Println("Khởi Tạo Mock Database Thành Công!")

	// Tạo admin user mặc định
	createDefaultAdmin()
}

// createDefaultAdmin Tạo Admin Mặc Định
func createDefaultAdmin() {
	// Hash password "admin123"
	hashedPassword := "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi" // admin123

	MockUsers[1] = map[string]interface{}{
		"id":         1,
		"username":   "admin",
		"email":      "<EMAIL>",
		"password":   hashedPassword,
		"full_name":  "Administrator",
		"role":       "admin",
		"google_id":  "",
		"avatar":     "",
		"created_at": "2024-01-01 00:00:00",
		"updated_at": "2024-01-01 00:00:00",
	}

	UserIDCounter = 2
	log.Println("Tạo Admin Mặc Định Thành Công! Email: <EMAIL>, Password: admin123")
}
