package config

import (
	"database/sql"
	"log"

	_ "github.com/mattn/go-sqlite3"
)

var DB *sql.DB

// InitDatabase khởi tạo kết nối SQLite
func InitDatabase() {
	var err error
	DB, err = sql.Open("sqlite3", "./panel.db")
	if err != nil {
		log.Fatal("Không thể kết nối database:", err)
	}

	// Kiểm tra kết nối
	if err = DB.Ping(); err != nil {
		log.Fatal("Không thể ping database:", err)
	}

	log.Println("Kết nối database thành công!")

	// Tạo bảng users nếu chưa tồn tại
	createUsersTable()
}

// createUsersTable tạo bảng users
func createUsersTable() {
	query := `
	CREATE TABLE IF NOT EXISTS users (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		username VARCHAR(50) UNIQUE NOT NULL,
		email VARCHAR(100) UNIQUE NOT NULL,
		password VARCHAR(255) NOT NULL,
		full_name VARCHAR(100),
		role VARCHAR(20) DEFAULT 'user',
		google_id VARCHAR(100),
		avatar VARCHAR(255),
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	_, err := DB.Exec(query)
	if err != nil {
		log.Fatal("Không thể tạo bảng users:", err)
	}

	log.Println("Bảng users đã được tạo hoặc đã tồn tại!")
}
