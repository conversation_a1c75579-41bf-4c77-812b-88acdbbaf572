{{define "content"}}
<div class="profile-container">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="profile-avatar-section">
            <div class="avatar-container">
                <img src="{{if .user.Avatar}}{{.user.Avatar}}{{else}}/static/images/default-avatar.png{{end}}" 
                     alt="Avatar" class="profile-avatar" id="avatarPreview">
                <div class="avatar-overlay">
                    <i class="fas fa-camera"></i>
                    <input type="file" id="avatarInput" accept="image/*" style="display: none;">
                </div>
            </div>
            <button type="button" class="btn btn-secondary" onclick="document.getElementById('avatarInput').click()">
                <i class="fas fa-upload"></i> Thay Đổi Avatar
            </button>
        </div>
        
        <div class="profile-info">
            <h1>{{.user.FullName}}</h1>
            <p class="profile-role">
                <i class="fas fa-{{if eq .user.Role "admin"}}crown{{else}}user{{end}}"></i>
                {{if eq .user.Role "admin"}}Quản Trị Viên{{else}}Người Dùng{{end}}
            </p>
            <p class="profile-email">
                <i class="fas fa-envelope"></i>
                {{.user.Email}}
            </p>
            <p class="profile-joined">
                <i class="fas fa-calendar"></i>
                Tham Gia: {{.user.CreatedAt.Format "02/01/2006"}}
            </p>
        </div>
    </div>

    <!-- Profile Content -->
    <div class="profile-content">
        <!-- Personal Information -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-user"></i> Thông Tin Cá Nhân</h3>
            </div>
            <form action="/home/<USER>" method="POST" class="profile-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="full_name" class="form-label">Họ Và Tên</label>
                        <input type="text" 
                               id="full_name" 
                               name="full_name" 
                               class="form-input" 
                               value="{{.user.FullName}}"
                               required>
                    </div>

                    <div class="form-group">
                        <label for="username" class="form-label">Tên Đăng Nhập</label>
                        <input type="text" 
                               id="username" 
                               name="username" 
                               class="form-input" 
                               value="{{.user.Username}}"
                               required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               class="form-input" 
                               value="{{.user.Email}}"
                               readonly>
                        <small class="form-help">Email không thể thay đổi</small>
                    </div>

                    <div class="form-group">
                        <label for="phone" class="form-label">Số Điện Thoại</label>
                        <input type="tel" 
                               id="phone" 
                               name="phone" 
                               class="form-input" 
                               placeholder="Nhập số điện thoại">
                    </div>
                </div>

                <div class="form-group">
                    <label for="bio" class="form-label">Giới Thiệu</label>
                    <textarea id="bio" 
                              name="bio" 
                              class="form-input" 
                              rows="4" 
                              placeholder="Viết vài dòng về bản thân..."></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu Thay Đổi
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Hủy Bỏ
                    </button>
                </div>
            </form>
        </div>

        <!-- Security Settings -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-shield-alt"></i> Bảo Mật</h3>
            </div>
            <form action="/home/<USER>" method="POST" class="security-form">
                <div class="form-group">
                    <label for="current_password" class="form-label">Mật Khẩu Hiện Tại</label>
                    <div class="password-input-group">
                        <input type="password" 
                               id="current_password" 
                               name="current_password" 
                               class="form-input" 
                               placeholder="Nhập mật khẩu hiện tại"
                               required>
                        <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="new_password" class="form-label">Mật Khẩu Mới</label>
                        <div class="password-input-group">
                            <input type="password" 
                                   id="new_password" 
                                   name="new_password" 
                                   class="form-input" 
                                   placeholder="Nhập mật khẩu mới"
                                   required>
                            <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password" class="form-label">Xác Nhận Mật Khẩu</label>
                        <div class="password-input-group">
                            <input type="password" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   class="form-input" 
                                   placeholder="Nhập lại mật khẩu mới"
                                   required>
                            <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="password-requirements">
                    <h4>Yêu Cầu Mật Khẩu:</h4>
                    <ul>
                        <li id="length-check">Ít nhất 8 ký tự</li>
                        <li id="uppercase-check">Có chữ hoa</li>
                        <li id="lowercase-check">Có chữ thường</li>
                        <li id="number-check">Có số</li>
                        <li id="special-check">Có ký tự đặc biệt</li>
                    </ul>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-key"></i> Đổi Mật Khẩu
                    </button>
                </div>
            </form>
        </div>

        <!-- Activity Log -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-history"></i> Lịch Sử Hoạt Động</h3>
            </div>
            <div class="activity-log">
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-sign-in-alt"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Đăng nhập</strong> từ Chrome trên Windows</p>
                        <span class="activity-time">Hôm nay, 14:30</span>
                        <span class="activity-location">IP: *********** - Hà Nội, Việt Nam</span>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Cập nhật</strong> thông tin cá nhân</p>
                        <span class="activity-time">Hôm qua, 09:15</span>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Thay đổi</strong> mật khẩu</p>
                        <span class="activity-time">3 ngày trước, 16:45</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-container {
    padding: 2rem 0;
}

.profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    display: flex;
    gap: 2rem;
    align-items: center;
}

.profile-avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.avatar-container {
    position: relative;
    cursor: pointer;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid rgba(255,255,255,0.3);
    object-fit: cover;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.avatar-container:hover .avatar-overlay {
    opacity: 1;
}

.avatar-overlay i {
    font-size: 2rem;
    color: white;
}

.profile-info h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.profile-role,
.profile-email,
.profile-joined {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.profile-content {
    display: grid;
    gap: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 0;
}

.form-help {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.password-requirements {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin: 1rem 0;
}

.password-requirements h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.password-requirements ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.password-requirements li {
    padding: 0.25rem 0;
    color: #666;
    position: relative;
    padding-left: 1.5rem;
}

.password-requirements li::before {
    content: '✗';
    position: absolute;
    left: 0;
    color: #dc3545;
}

.password-requirements li.valid::before {
    content: '✓';
    color: #28a745;
}

.activity-log {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #667eea;
    flex-shrink: 0;
}

.activity-content p {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
}

.activity-time,
.activity-location {
    display: block;
    font-size: 0.8rem;
    color: #666;
}

@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>
{{end}}

{{define "scripts"}}
<script>
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

// Avatar upload preview
document.getElementById('avatarInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('avatarPreview').src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
});

// Password strength validation
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    
    // Check length
    const lengthCheck = document.getElementById('length-check');
    if (password.length >= 8) {
        lengthCheck.classList.add('valid');
    } else {
        lengthCheck.classList.remove('valid');
    }
    
    // Check uppercase
    const uppercaseCheck = document.getElementById('uppercase-check');
    if (/[A-Z]/.test(password)) {
        uppercaseCheck.classList.add('valid');
    } else {
        uppercaseCheck.classList.remove('valid');
    }
    
    // Check lowercase
    const lowercaseCheck = document.getElementById('lowercase-check');
    if (/[a-z]/.test(password)) {
        lowercaseCheck.classList.add('valid');
    } else {
        lowercaseCheck.classList.remove('valid');
    }
    
    // Check number
    const numberCheck = document.getElementById('number-check');
    if (/[0-9]/.test(password)) {
        numberCheck.classList.add('valid');
    } else {
        numberCheck.classList.remove('valid');
    }
    
    // Check special character
    const specialCheck = document.getElementById('special-check');
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        specialCheck.classList.add('valid');
    } else {
        specialCheck.classList.remove('valid');
    }
});
</script>
{{end}}
