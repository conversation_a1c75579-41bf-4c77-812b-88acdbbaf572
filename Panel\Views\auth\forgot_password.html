{{define "content"}}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="fas fa-chart-line"></i>
                <h1>Panel Admin</h1>
            </div>
            <h2>Quên <PERSON></h2>
            <p>Nhập Email Của Bạn Để Nhận Liên Kết Đặt Lại Mật K<PERSON>ẩu.</p>
        </div>

        <form action="/auth/forgot-password" method="POST" class="auth-form">
            <div class="form-group">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope"></i>
                    Email
                </label>
                <input type="email" 
                       id="email" 
                       name="email" 
                       class="form-input" 
                       placeholder="Nhập Địa Chỉ Email Của Bạn"
                       required>
            </div>

            <button type="submit" class="btn btn-primary btn-full">
                <i class="fas fa-paper-plane"></i>
                G<PERSON>i <PERSON>ên <PERSON>ết Đặt Lại
            </button>
        </form>

        <div class="auth-info">
            <div class="info-box">
                <i class="fas fa-info-circle"></i>
                <div>
                    <h4>Hướng Dẫn</h4>
                    <ul>
                        <li>Nhập Email Đã Đăng Ký Tài Khoản</li>
                        <li>Kiểm Tra Hộp Thư Đến Và Thư Rác</li>
                        <li>Nhấp Vào Liên Kết Trong Email</li>
                        <li>Tạo Mật Khẩu Mới</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="auth-footer">
            <p>Nhớ Mật Khẩu? <a href="/auth/login">Đăng Nhập</a></p>
            <p>Chưa Có Tài Khoản? <a href="/auth/register">Đăng Ký Ngay</a></p>
        </div>
    </div>
</div>

<style>
.auth-container {
    min-height: calc(100vh - 120px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    width: 100%;
    max-width: 450px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.auth-logo i {
    font-size: 2rem;
    color: #667eea;
}

.auth-logo h1 {
    font-size: 1.8rem;
    color: #333;
    margin: 0;
}

.auth-header h2 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.auth-header p {
    color: #666;
    font-size: 0.9rem;
}

.auth-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #555;
}

.btn-full {
    width: 100%;
    padding: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.auth-info {
    margin-bottom: 2rem;
}

.info-box {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
}

.info-box i {
    color: #667eea;
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.info-box h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
    font-size: 1rem;
}

.info-box ul {
    margin: 0;
    padding-left: 1rem;
    color: #666;
    font-size: 0.9rem;
}

.info-box li {
    margin-bottom: 0.25rem;
}

.auth-footer {
    text-align: center;
    margin-top: 2rem;
}

.auth-footer p {
    margin-bottom: 0.5rem;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.auth-footer a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .auth-card {
        margin: 1rem;
        padding: 2rem;
    }
    
    .info-box {
        flex-direction: column;
        text-align: center;
    }
}
</style>
{{end}}

{{define "scripts"}}
<script>
// Form submission handling
document.querySelector('.auth-form').addEventListener('submit', function(e) {
    const email = document.getElementById('email').value;
    
    if (!email) {
        e.preventDefault();
        PanelApp.showAlert('Vui Lòng Nhập Email', 'error');
        return;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang Gửi...';
    submitBtn.disabled = true;
    
    // Re-enable button after 3 seconds (in case of error)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);
});
</script>
{{end}}
