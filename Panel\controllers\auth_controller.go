package controllers

import (
	"net/http"
	"panel/models"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

// ShowLogin hiển thị trang đăng nhập
func ShowLogin(c *gin.Context) {
	c.HTML(http.StatusOK, "auth/login.html", gin.H{
		"title": "Đăng nhập",
	})
}

// ShowRegister hiển thị trang đăng ký
func ShowRegister(c *gin.Context) {
	c.HTML(http.StatusOK, "auth/register.html", gin.H{
		"title": "Đăng ký",
	})
}

// ShowForgotPassword hiển thị trang quên mật khẩu
func ShowForgotPassword(c *gin.Context) {
	c.HTML(http.StatusOK, "auth/forgot_password.html", gin.H{
		"title": "Quên mật khẩu",
	})
}

// Login xử lý đăng nhập
func Login(c *gin.Context) {
	email := c.<PERSON>orm("email")
	password := c.<PERSON>orm("password")

	if email == "" || password == "" {
		c.HTML(http.StatusBadRequest, "auth/login.html", gin.H{
			"title": "Đăng nhập",
			"error": "Vui lòng nhập đầy đủ thông tin",
		})
		return
	}

	// Tìm user theo email
	user, err := models.GetUserByEmail(email)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "auth/login.html", gin.H{
			"title": "Đăng nhập",
			"error": "Lỗi hệ thống, vui lòng thử lại",
		})
		return
	}

	if user == nil || !user.ValidatePassword(password) {
		c.HTML(http.StatusBadRequest, "auth/login.html", gin.H{
			"title": "Đăng nhập",
			"error": "Email hoặc mật khẩu không đúng",
		})
		return
	}

	// Lưu session
	session := sessions.Default(c)
	session.Set("user_id", user.ID)
	session.Save()

	c.Redirect(http.StatusFound, "/")
}

// Register xử lý đăng ký
func Register(c *gin.Context) {
	username := c.PostForm("username")
	email := c.PostForm("email")
	password := c.PostForm("password")
	confirmPassword := c.PostForm("confirm_password")
	fullName := c.PostForm("full_name")

	// Validate input
	if username == "" || email == "" || password == "" || fullName == "" {
		c.HTML(http.StatusBadRequest, "auth/register.html", gin.H{
			"title": "Đăng ký",
			"error": "Vui lòng nhập đầy đủ thông tin",
		})
		return
	}

	if password != confirmPassword {
		c.HTML(http.StatusBadRequest, "auth/register.html", gin.H{
			"title": "Đăng ký",
			"error": "Mật khẩu xác nhận không khớp",
		})
		return
	}

	// Kiểm tra email đã tồn tại
	existingUser, err := models.GetUserByEmail(email)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "auth/register.html", gin.H{
			"title": "Đăng ký",
			"error": "Lỗi hệ thống, vui lòng thử lại",
		})
		return
	}

	if existingUser != nil {
		c.HTML(http.StatusBadRequest, "auth/register.html", gin.H{
			"title": "Đăng ký",
			"error": "Email đã được sử dụng",
		})
		return
	}

	// Tạo user mới
	user := &models.User{
		Username: username,
		Email:    email,
		Password: password,
		FullName: fullName,
		Role:     "user",
	}

	err = models.CreateUser(user)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "auth/register.html", gin.H{
			"title": "Đăng ký",
			"error": "Không thể tạo tài khoản, vui lòng thử lại",
		})
		return
	}

	// Tự động đăng nhập sau khi đăng ký
	session := sessions.Default(c)
	session.Set("user_id", user.ID)
	session.Save()

	c.Redirect(http.StatusFound, "/")
}

// Logout xử lý đăng xuất
func Logout(c *gin.Context) {
	session := sessions.Default(c)
	session.Delete("user_id")
	session.Save()

	c.Redirect(http.StatusFound, "/auth/login")
}
