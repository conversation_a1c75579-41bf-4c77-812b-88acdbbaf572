package middleware

import (
	"net/http"
	"panel/models"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

// AuthRequired Middleware Yêu <PERSON>
func AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		session := sessions.Default(c)
		userID := session.Get("user_id")

		if userID == nil {
			c.Redirect(http.StatusFound, "/auth/login")
			c.Abort()
			return
		}

		// Lấy Thông Tin User Và Lưu V<PERSON>o Context
		user, err := models.GetUserByID(userID.(int))
		if err != nil || user == nil {
			session.Delete("user_id")
			session.Save()
			c.Redirect(http.StatusFound, "/auth/login")
			c.Abort()
			return
		}

		c.Set("user", user)
		c.Next()
	}
}

// AdminRequired Middleware Yêu <PERSON>n Admin
func AdminRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.Redirect(http.StatusFound, "/auth/login")
			c.Abort()
			return
		}

		u := user.(*models.User)
		if u.Role != "admin" {
			c.HTML(http.StatusForbidden, "error.html", gin.H{
				"title":   "Không Có Quyền Truy Cập",
				"message": "Bạn Không Có Quyền Truy Cập Trang Này",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GuestOnly Middleware Chỉ Cho Phép Khách (Chưa Đăng Nhập)
func GuestOnly() gin.HandlerFunc {
	return func(c *gin.Context) {
		session := sessions.Default(c)
		userID := session.Get("user_id")

		if userID != nil {
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		c.Next()
	}
}
