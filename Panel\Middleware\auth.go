package middleware

import (
	"net/http"
	"panel/models"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

// AuthRequired middleware yêu cầu đăng nhập
func AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		session := sessions.Default(c)
		userID := session.Get("user_id")

		if userID == nil {
			c.Redirect(http.StatusFound, "/auth/login")
			c.Abort()
			return
		}

		// Lấy thông tin user và lưu vào context
		user, err := models.GetUserByID(userID.(int))
		if err != nil || user == nil {
			session.Delete("user_id")
			session.Save()
			c.Redirect(http.StatusFound, "/auth/login")
			c.Abort()
			return
		}

		c.Set("user", user)
		c.Next()
	}
}

// AdminRequired middleware yêu cầu quyền admin
func AdminRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.Redirect(http.StatusFound, "/auth/login")
			c.Abort()
			return
		}

		u := user.(*models.User)
		if u.Role != "admin" {
			c.HTML(http.StatusForbidden, "error.html", gin.H{
				"title":   "Không có quyền truy cập",
				"message": "Bạn không có quyền truy cập trang này",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GuestOnly middleware chỉ cho phép khách (chưa đăng nhập)
func GuestOnly() gin.HandlerFunc {
	return func(c *gin.Context) {
		session := sessions.Default(c)
		userID := session.Get("user_id")

		if userID != nil {
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		c.Next()
	}
}
