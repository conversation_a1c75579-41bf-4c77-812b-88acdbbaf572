{{define "content"}}
<div class="chat-container">
    <!-- Chat Sidebar -->
    <div class="chat-sidebar">
        <div class="chat-header">
            <h3><i class="fas fa-comments"></i> Chat</h3>
            <button class="btn btn-primary btn-sm" onclick="startNewChat()">
                <i class="fas fa-plus"></i> Cuộc Trò Chuyện Mới
            </button>
        </div>

        <!-- Search -->
        <div class="chat-search">
            <div class="search-input-group">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="Tìm kiếm cuộc trò chuyện..." class="search-input">
            </div>
        </div>

        <!-- Chat List -->
        <div class="chat-list">
            <div class="chat-item active" data-chat-id="1">
                <div class="chat-avatar">
                    <img src="/static/images/avatar1.jpg" alt="Avatar">
                    <span class="status-indicator online"></span>
                </div>
                <div class="chat-info">
                    <h4>Nguyễn Văn A</h4>
                    <p>Xin chào, tôi cần hỗ trợ...</p>
                    <span class="chat-time">14:30</span>
                </div>
                <div class="chat-badge">3</div>
            </div>

            <div class="chat-item" data-chat-id="2">
                <div class="chat-avatar">
                    <img src="/static/images/avatar2.jpg" alt="Avatar">
                    <span class="status-indicator away"></span>
                </div>
                <div class="chat-info">
                    <h4>Trần Thị B</h4>
                    <p>Cảm ơn bạn đã hỗ trợ!</p>
                    <span class="chat-time">13:45</span>
                </div>
            </div>

            <div class="chat-item" data-chat-id="3">
                <div class="chat-avatar">
                    <img src="/static/images/avatar3.jpg" alt="Avatar">
                    <span class="status-indicator offline"></span>
                </div>
                <div class="chat-info">
                    <h4>Lê Văn C</h4>
                    <p>Tôi sẽ liên hệ lại sau</p>
                    <span class="chat-time">Hôm qua</span>
                </div>
            </div>

            <div class="chat-item" data-chat-id="4">
                <div class="chat-avatar">
                    <img src="/static/images/avatar4.jpg" alt="Avatar">
                    <span class="status-indicator online"></span>
                </div>
                <div class="chat-info">
                    <h4>Phạm Thị D</h4>
                    <p>Được rồi, tôi hiểu</p>
                    <span class="chat-time">2 ngày trước</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Main -->
    <div class="chat-main">
        <!-- Chat Header -->
        <div class="chat-main-header">
            <div class="chat-user-info">
                <div class="chat-avatar">
                    <img src="/static/images/avatar1.jpg" alt="Avatar">
                    <span class="status-indicator online"></span>
                </div>
                <div class="user-details">
                    <h4>Nguyễn Văn A</h4>
                    <span class="user-status">Đang hoạt động</span>
                </div>
            </div>
            <div class="chat-actions">
                <button class="btn btn-icon" title="Gọi điện">
                    <i class="fas fa-phone"></i>
                </button>
                <button class="btn btn-icon" title="Video call">
                    <i class="fas fa-video"></i>
                </button>
                <button class="btn btn-icon" title="Thông tin">
                    <i class="fas fa-info-circle"></i>
                </button>
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="chat-messages" id="chatMessages">
            <div class="message-group">
                <div class="message received">
                    <div class="message-avatar">
                        <img src="/static/images/avatar1.jpg" alt="Avatar">
                    </div>
                    <div class="message-content">
                        <div class="message-bubble">
                            Xin chào! Tôi cần hỗ trợ về sản phẩm của bạn.
                        </div>
                        <div class="message-time">14:25</div>
                    </div>
                </div>
            </div>

            <div class="message-group">
                <div class="message sent">
                    <div class="message-content">
                        <div class="message-bubble">
                            Chào bạn! Tôi rất sẵn lòng hỗ trợ. Bạn gặp vấn đề gì vậy?
                        </div>
                        <div class="message-time">14:26</div>
                    </div>
                </div>
            </div>

            <div class="message-group">
                <div class="message received">
                    <div class="message-avatar">
                        <img src="/static/images/avatar1.jpg" alt="Avatar">
                    </div>
                    <div class="message-content">
                        <div class="message-bubble">
                            Tôi không thể đăng nhập vào tài khoản của mình. Có thể bạn giúp tôi kiểm tra được không?
                        </div>
                        <div class="message-time">14:28</div>
                    </div>
                </div>
            </div>

            <div class="message-group">
                <div class="message sent">
                    <div class="message-content">
                        <div class="message-bubble">
                            Được thôi! Bạn có thể cho tôi biết email đăng ký tài khoản không?
                        </div>
                        <div class="message-time">14:29</div>
                    </div>
                </div>
            </div>

            <div class="typing-indicator">
                <div class="typing-avatar">
                    <img src="/static/images/avatar1.jpg" alt="Avatar">
                </div>
                <div class="typing-bubble">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="chat-input-area">
            <div class="input-actions">
                <button class="btn btn-icon" title="Đính kèm file">
                    <i class="fas fa-paperclip"></i>
                </button>
                <button class="btn btn-icon" title="Emoji">
                    <i class="fas fa-smile"></i>
                </button>
            </div>
            <div class="input-field">
                <textarea class="chat-input" 
                          placeholder="Nhập tin nhắn..." 
                          rows="1"></textarea>
            </div>
            <button class="btn btn-primary send-btn" onclick="sendMessage()">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <!-- Chat Info Panel (Hidden by default) -->
    <div class="chat-info-panel" id="chatInfoPanel">
        <div class="info-header">
            <h3>Thông Tin</h3>
            <button class="btn btn-icon" onclick="toggleInfoPanel()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="info-content">
            <div class="user-profile">
                <img src="/static/images/avatar1.jpg" alt="Avatar" class="profile-avatar">
                <h4>Nguyễn Văn A</h4>
                <p><EMAIL></p>
                <span class="user-role">Khách hàng</span>
            </div>

            <div class="info-section">
                <h5>Tệp Đính Kèm</h5>
                <div class="attachment-list">
                    <div class="attachment-item">
                        <i class="fas fa-file-pdf"></i>
                        <span>document.pdf</span>
                    </div>
                    <div class="attachment-item">
                        <i class="fas fa-image"></i>
                        <span>screenshot.png</span>
                    </div>
                </div>
            </div>

            <div class="info-section">
                <h5>Hành Động</h5>
                <div class="action-buttons">
                    <button class="btn btn-secondary btn-full">
                        <i class="fas fa-ban"></i> Chặn Người Dùng
                    </button>
                    <button class="btn btn-danger btn-full">
                        <i class="fas fa-trash"></i> Xóa Cuộc Trò Chuyện
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.chat-container {
    display: flex;
    height: calc(100vh - 140px);
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.chat-sidebar {
    width: 350px;
    border-right: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
}

.chat-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h3 {
    margin: 0;
    color: #333;
}

.chat-search {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.search-input-group {
    position: relative;
}

.search-input-group i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-input {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    border: 1px solid #ddd;
    border-radius: 25px;
    background: #f8f9fa;
}

.chat-list {
    flex: 1;
    overflow-y: auto;
}

.chat-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: background 0.3s;
    position: relative;
}

.chat-item:hover {
    background: #f8f9fa;
}

.chat-item.active {
    background: #667eea;
    color: white;
}

.chat-avatar {
    position: relative;
    margin-right: 1rem;
}

.chat-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.status-indicator.online { background: #28a745; }
.status-indicator.away { background: #ffc107; }
.status-indicator.offline { background: #6c757d; }

.chat-info {
    flex: 1;
    min-width: 0;
}

.chat-info h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.chat-info p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-time {
    font-size: 0.8rem;
    opacity: 0.7;
}

.chat-badge {
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-main-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-details h4 {
    margin: 0;
    font-size: 1.1rem;
}

.user-status {
    font-size: 0.9rem;
    color: #28a745;
}

.chat-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: #f8f9fa;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-icon:hover {
    background: #667eea;
    color: white;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background: #f8f9fa;
}

.message-group {
    margin-bottom: 1rem;
}

.message {
    display: flex;
    margin-bottom: 0.5rem;
}

.message.sent {
    justify-content: flex-end;
}

.message-avatar {
    margin-right: 0.5rem;
}

.message-avatar img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
}

.message-content {
    max-width: 70%;
}

.message-bubble {
    padding: 0.75rem 1rem;
    border-radius: 18px;
    word-wrap: break-word;
}

.message.received .message-bubble {
    background: white;
    color: #333;
}

.message.sent .message-bubble {
    background: #667eea;
    color: white;
}

.message-time {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
    text-align: right;
}

.message.received .message-time {
    text-align: left;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.7;
}

.typing-avatar img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
}

.typing-bubble {
    background: white;
    padding: 0.75rem 1rem;
    border-radius: 18px;
}

.typing-dots {
    display: flex;
    gap: 0.25rem;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #666;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) { animation-delay: 0.2s; }
.typing-dots span:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-10px); }
}

.chat-input-area {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    align-items: flex-end;
    gap: 1rem;
    background: white;
}

.input-actions {
    display: flex;
    gap: 0.5rem;
}

.input-field {
    flex: 1;
}

.chat-input {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 25px;
    padding: 0.75rem 1rem;
    resize: none;
    max-height: 100px;
}

.send-btn {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-info-panel {
    width: 300px;
    border-left: 1px solid #e9ecef;
    background: white;
    display: none;
}

.chat-info-panel.active {
    display: block;
}

.info-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-content {
    padding: 1.5rem;
}

.user-profile {
    text-align: center;
    margin-bottom: 2rem;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 1rem;
}

.user-role {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.info-section {
    margin-bottom: 2rem;
}

.info-section h5 {
    margin-bottom: 1rem;
    color: #333;
}

.attachment-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.attachment-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.btn-full {
    width: 100%;
}

@media (max-width: 768px) {
    .chat-container {
        height: calc(100vh - 80px);
    }
    
    .chat-sidebar {
        width: 100%;
        position: absolute;
        z-index: 10;
        background: white;
        transform: translateX(-100%);
        transition: transform 0.3s;
    }
    
    .chat-sidebar.active {
        transform: translateX(0);
    }
    
    .chat-info-panel {
        display: none !important;
    }
}
</style>
{{end}}

{{define "scripts"}}
<script>
function sendMessage() {
    const input = document.querySelector('.chat-input');
    const message = input.value.trim();
    
    if (!message) return;
    
    // Add message to chat
    addMessage(message, true);
    input.value = '';
    
    // Auto-resize textarea
    input.style.height = 'auto';
    
    // Simulate response
    setTimeout(() => {
        addMessage('Cảm ơn bạn đã liên hệ! Tôi sẽ xử lý yêu cầu của bạn ngay.', false);
    }, 1000);
}

function addMessage(text, isSent) {
    const messagesContainer = document.getElementById('chatMessages');
    const messageGroup = document.createElement('div');
    messageGroup.className = 'message-group';
    
    const message = document.createElement('div');
    message.className = `message ${isSent ? 'sent' : 'received'}`;
    
    let messageHTML = '';
    
    if (!isSent) {
        messageHTML += `
            <div class="message-avatar">
                <img src="/static/images/avatar1.jpg" alt="Avatar">
            </div>
        `;
    }
    
    messageHTML += `
        <div class="message-content">
            <div class="message-bubble">${escapeHtml(text)}</div>
            <div class="message-time">${new Date().toLocaleTimeString('vi-VN', {hour: '2-digit', minute: '2-digit'})}</div>
        </div>
    `;
    
    message.innerHTML = messageHTML;
    messageGroup.appendChild(message);
    
    // Remove typing indicator
    const typingIndicator = messagesContainer.querySelector('.typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
    
    messagesContainer.appendChild(messageGroup);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function toggleInfoPanel() {
    const panel = document.getElementById('chatInfoPanel');
    panel.classList.toggle('active');
}

function startNewChat() {
    // Implement new chat functionality
    console.log('Starting new chat...');
}

// Auto-resize textarea
document.querySelector('.chat-input').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = Math.min(this.scrollHeight, 100) + 'px';
});

// Send message on Enter
document.querySelector('.chat-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
    }
});

// Chat item selection
document.querySelectorAll('.chat-item').forEach(item => {
    item.addEventListener('click', function() {
        document.querySelectorAll('.chat-item').forEach(i => i.classList.remove('active'));
        this.classList.add('active');
        
        // Load chat messages for selected user
        // This would typically fetch from API
        console.log('Loading chat for user:', this.dataset.chatId);
    });
});
</script>
{{end}}
