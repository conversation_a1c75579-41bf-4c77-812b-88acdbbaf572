package models

import (
	"database/sql"
	"panel/config"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// User struct đ<PERSON>i diện cho người dùng
type User struct {
	ID        int       `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Password  string    `json:"-"` // Không trả về password trong JSON
	FullName  string    `json:"full_name"`
	Role      string    `json:"role"`
	GoogleID  string    `json:"google_id"`
	Avatar    string    `json:"avatar"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CreateUser tạo người dùng mới
func CreateUser(user *User) error {
	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	query := `
		INSERT INTO users (username, email, password, full_name, role, google_id, avatar)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	result, err := config.DB.Exec(query, user.Username, user.Email, string(hashedPassword), 
		user.FullName, user.Role, user.GoogleID, user.Avatar)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	user.ID = int(id)
	return nil
}

// GetUserByEmail lấy người dùng theo email
func GetUserByEmail(email string) (*User, error) {
	user := &User{}
	query := `
		SELECT id, username, email, password, full_name, role, google_id, avatar, created_at, updated_at
		FROM users WHERE email = ?
	`

	err := config.DB.QueryRow(query, email).Scan(
		&user.ID, &user.Username, &user.Email, &user.Password,
		&user.FullName, &user.Role, &user.GoogleID, &user.Avatar,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return user, nil
}

// GetUserByID lấy người dùng theo ID
func GetUserByID(id int) (*User, error) {
	user := &User{}
	query := `
		SELECT id, username, email, password, full_name, role, google_id, avatar, created_at, updated_at
		FROM users WHERE id = ?
	`

	err := config.DB.QueryRow(query, id).Scan(
		&user.ID, &user.Username, &user.Email, &user.Password,
		&user.FullName, &user.Role, &user.GoogleID, &user.Avatar,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return user, nil
}

// ValidatePassword kiểm tra mật khẩu
func (u *User) ValidatePassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
	return err == nil
}

// GetAllUsers lấy tất cả người dùng (cho admin)
func GetAllUsers() ([]User, error) {
	query := `
		SELECT id, username, email, full_name, role, google_id, avatar, created_at, updated_at
		FROM users ORDER BY created_at DESC
	`

	rows, err := config.DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var users []User
	for rows.Next() {
		var user User
		err := rows.Scan(
			&user.ID, &user.Username, &user.Email,
			&user.FullName, &user.Role, &user.GoogleID, &user.Avatar,
			&user.CreatedAt, &user.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		users = append(users, user)
	}

	return users, nil
}
