package models

import (
	"errors"
	"panel/config"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// User Struct Đ<PERSON><PERSON>
type User struct {
	ID        int       `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Password  string    `json:"-"` // Không Trả Về Password Trong JSON
	FullName  string    `json:"full_name"`
	Role      string    `json:"role"`
	GoogleID  string    `json:"google_id"`
	Avatar    string    `json:"avatar"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CreateUser Tạo Người Dùng Mới
func CreateUser(user *User) error {
	// Kiểm tra email đã tồn tại
	for _, userData := range config.MockUsers {
		if userData["email"].(string) == user.Email {
			return errors.New("email đã được sử dụng")
		}
	}

	// Hash Password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// Tạo user mới trong mock database
	user.ID = config.UserIDCounter
	config.MockUsers[user.ID] = map[string]interface{}{
		"id":         user.ID,
		"username":   user.Username,
		"email":      user.Email,
		"password":   string(hashedPassword),
		"full_name":  user.FullName,
		"role":       user.Role,
		"google_id":  user.GoogleID,
		"avatar":     user.Avatar,
		"created_at": time.Now().Format("2006-01-02 15:04:05"),
		"updated_at": time.Now().Format("2006-01-02 15:04:05"),
	}

	config.UserIDCounter++
	return nil
}

// GetUserByEmail Lấy Người Dùng Theo Email
func GetUserByEmail(email string) (*User, error) {
	for _, userData := range config.MockUsers {
		if userData["email"].(string) == email {
			createdAt, _ := time.Parse("2006-01-02 15:04:05", userData["created_at"].(string))
			updatedAt, _ := time.Parse("2006-01-02 15:04:05", userData["updated_at"].(string))

			return &User{
				ID:        userData["id"].(int),
				Username:  userData["username"].(string),
				Email:     userData["email"].(string),
				Password:  userData["password"].(string),
				FullName:  userData["full_name"].(string),
				Role:      userData["role"].(string),
				GoogleID:  userData["google_id"].(string),
				Avatar:    userData["avatar"].(string),
				CreatedAt: createdAt,
				UpdatedAt: updatedAt,
			}, nil
		}
	}
	return nil, nil
}

// GetUserByID Lấy Người Dùng Theo ID
func GetUserByID(id int) (*User, error) {
	userData, exists := config.MockUsers[id]
	if !exists {
		return nil, nil
	}

	createdAt, _ := time.Parse("2006-01-02 15:04:05", userData["created_at"].(string))
	updatedAt, _ := time.Parse("2006-01-02 15:04:05", userData["updated_at"].(string))

	return &User{
		ID:        userData["id"].(int),
		Username:  userData["username"].(string),
		Email:     userData["email"].(string),
		Password:  userData["password"].(string),
		FullName:  userData["full_name"].(string),
		Role:      userData["role"].(string),
		GoogleID:  userData["google_id"].(string),
		Avatar:    userData["avatar"].(string),
		CreatedAt: createdAt,
		UpdatedAt: updatedAt,
	}, nil
}

// ValidatePassword Kiểm Tra Mật Khẩu
func (u *User) ValidatePassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
	return err == nil
}

// GetAllUsers Lấy Tất Cả Người Dùng (Cho Admin)
func GetAllUsers() ([]User, error) {
	var users []User

	for _, userData := range config.MockUsers {
		createdAt, _ := time.Parse("2006-01-02 15:04:05", userData["created_at"].(string))
		updatedAt, _ := time.Parse("2006-01-02 15:04:05", userData["updated_at"].(string))

		user := User{
			ID:        userData["id"].(int),
			Username:  userData["username"].(string),
			Email:     userData["email"].(string),
			FullName:  userData["full_name"].(string),
			Role:      userData["role"].(string),
			GoogleID:  userData["google_id"].(string),
			Avatar:    userData["avatar"].(string),
			CreatedAt: createdAt,
			UpdatedAt: updatedAt,
		}
		users = append(users, user)
	}

	return users, nil
}
