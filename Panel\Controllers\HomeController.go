package controllers

import (
	"net/http"
	"panel/models"

	"github.com/gin-gonic/gin"
)

// ShowHome Hiển Thị Trang Chủ
func ShowHome(c *gin.Context) {
	user, _ := c.Get("user")
	
	c.HTML(http.StatusOK, "home/index.html", gin.H{
		"title": "Trang Chủ",
		"user":  user,
	})
}

// ShowProfile Hiển Thị Trang Thông Tin Cá Nhân
func ShowProfile(c *gin.Context) {
	user, _ := c.Get("user")
	
	c.HTML(http.StatusOK, "home/profile.html", gin.H{
		"title": "Thông Tin Cá Nhân",
		"user":  user,
	})
}

// UpdateProfile Cập Nhật Thông Tin Cá Nhân
func UpdateProfile(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.Redirect(http.StatusFound, "/auth/login")
		return
	}

	currentUser := user.(*models.User)
	
	// L<PERSON><PERSON>ừ Form
	fullName := c.PostForm("full_name")
	username := c.PostForm("username")

	if fullName == "" || username == "" {
		c.HTML(http.StatusBadRequest, "home/profile.html", gin.H{
			"title": "Thông Tin Cá Nhân",
			"user":  currentUser,
			"error": "Vui Lòng Nhập Đầy Đủ Thông Tin",
		})
		return
	}

	// Cập Nhật Thông Tin (đây là ví dụ đơn giản, trong thực tế cần thêm method UpdateUser)
	c.HTML(http.StatusOK, "home/profile.html", gin.H{
		"title":   "Thông Tin Cá Nhân",
		"user":    currentUser,
		"success": "Cập Nhật Thông Tin Thành Công",
	})
}

// ShowChat Hiển Thị Trang Chat
func ShowChat(c *gin.Context) {
	user, _ := c.Get("user")
	
	c.HTML(http.StatusOK, "chat/chat.html", gin.H{
		"title": "Chat",
		"user":  user,
	})
}
