package routes

import (
	"panel/controllers"
	"panel/middleware"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
)

// SetupRoutes Thiết Lập Tất Cả Routes
func SetupRoutes(r *gin.Engine) {
	// Thiết Lập Session
	store := cookie.NewStore([]byte("secret-key-panel-app"))
	r.Use(sessions.Sessions("panel-session", store))

	// Routes Công Khai (Không Cần Đăng Nhập)
	public := r.Group("/")
	{
		// Redirect Root To Home
		public.GET("/", func(c *gin.Context) {
			c.Redirect(302, "/home")
		})
	}

	// Auth Routes (Chỉ Cho K<PERSON>ch <PERSON>ưa Đăng Nhập)
	auth := r.Group("/auth")
	auth.Use(middleware.GuestOnly())
	{
		auth.GET("/login", controllers.ShowLogin)
		auth.POST("/login", controllers.Login)
		auth.GET("/register", controllers.ShowRegister)
		auth.POST("/register", controllers.Register)
		auth.GET("/forgot-password", controllers.ShowForgotPassword)
	}

	// Logout Route (Cho Người Đã Đăng Nhập)
	r.POST("/auth/logout", middleware.AuthRequired(), controllers.Logout)

	// Home Routes (Cần Đăng Nhập)
	home := r.Group("/home")
	home.Use(middleware.AuthRequired())
	{
		home.GET("/", controllers.ShowHome)
		home.GET("/profile", controllers.ShowProfile)
		home.POST("/profile", controllers.UpdateProfile)
	}

	// Chat Routes (Cần Đăng Nhập)
	chat := r.Group("/chat")
	chat.Use(middleware.AuthRequired())
	{
		chat.GET("/", controllers.ShowChat)
	}

	// Admin Routes (Cần Đăng Nhập Và Quyền Admin)
	admin := r.Group("/admin")
	admin.Use(middleware.AuthRequired())
	admin.Use(middleware.AdminRequired())
	{
		admin.GET("/dashboard", controllers.ShowAdminDashboard)
	}

	// API Routes
	api := r.Group("/api")
	api.Use(middleware.AuthRequired())
	{
		api.GET("/user", controllers.GetUserAPI)
		
		// Admin API
		adminAPI := api.Group("/admin")
		adminAPI.Use(middleware.AdminRequired())
		{
			adminAPI.GET("/users", controllers.GetAllUsersAPI)
		}
	}
}
