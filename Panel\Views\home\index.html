{{define "content"}}
<div class="dashboard-container">
    <!-- Welcome Section -->
    <div class="welcome-section">
        <div class="welcome-content">
            <h1><PERSON><PERSON><PERSON> Trở Lại, {{.user.FullName}}!</h1>
            <p><PERSON><PERSON><PERSON> Tổng Quan Của Bạn. Hãy Khám Phá Các Tính Năng Mới.</p>
        </div>
        <div class="welcome-avatar">
            <img src="{{if .user.Avatar}}{{.user.Avatar}}{{else}}/static/images/default-avatar.png{{end}}" 
                 alt="Avatar" class="avatar-large">
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3 class="stat-number">1,234</h3>
                <p class="stat-label">Tổng <PERSON></p>
                <span class="stat-change positive">+12%</span>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-content">
                <h3 class="stat-number">89.5%</h3>
                <p class="stat-label">Tỷ Lệ Hoạt Động</p>
                <span class="stat-change positive">+5.2%</span>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-comments"></i>
            </div>
            <div class="stat-content">
                <h3 class="stat-number">456</h3>
                <p class="stat-label">Tin Nhắn Hôm Nay</p>
                <span class="stat-change negative">-2.1%</span>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="stat-content">
                <h3 class="stat-number">23</h3>
                <p class="stat-label">Nhiệm Vụ Đang Chờ</p>
                <span class="stat-change neutral">0%</span>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="content-grid">
        <!-- Recent Activities -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-clock"></i> Hoạt Động Gần Đây</h3>
                <a href="#" class="view-all">Xem Tất Cả</a>
            </div>
            <div class="activity-list">
                <div class="activity-item">
                    <div class="activity-avatar">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Người Dùng Mới</strong> đã đăng ký</p>
                        <span class="activity-time">5 phút trước</span>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-avatar">
                        <i class="fas fa-comment"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Tin Nhắn Mới</strong> từ khách hàng</p>
                        <span class="activity-time">15 phút trước</span>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-avatar">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Cài Đặt</strong> đã được cập nhật</p>
                        <span class="activity-time">1 giờ trước</span>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-avatar">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>Bảo Mật</strong> đã được kiểm tra</p>
                        <span class="activity-time">2 giờ trước</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-bolt"></i> Thao Tác Nhanh</h3>
            </div>
            <div class="quick-actions">
                <a href="/home/<USER>" class="action-btn">
                    <i class="fas fa-user-edit"></i>
                    <span>Chỉnh Sửa Hồ Sơ</span>
                </a>

                <a href="/chat" class="action-btn">
                    <i class="fas fa-comments"></i>
                    <span>Mở Chat</span>
                </a>

                <a href="/settings" class="action-btn">
                    <i class="fas fa-cog"></i>
                    <span>Cài Đặt</span>
                </a>

                {{if eq .user.Role "admin"}}
                <a href="/admin/dashboard" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Admin Panel</span>
                </a>
                {{end}}

                <a href="/reports" class="action-btn">
                    <i class="fas fa-chart-bar"></i>
                    <span>Báo Cáo</span>
                </a>

                <a href="/help" class="action-btn">
                    <i class="fas fa-question-circle"></i>
                    <span>Trợ Giúp</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Chart Section -->
    <div class="chart-section">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-chart-area"></i> Thống Kê Hoạt Động</h3>
                <div class="chart-controls">
                    <select class="chart-period">
                        <option value="7">7 Ngày Qua</option>
                        <option value="30">30 Ngày Qua</option>
                        <option value="90">3 Tháng Qua</option>
                    </select>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="activityChart"></canvas>
            </div>
        </div>
    </div>
</div>

<style>
.dashboard-container {
    padding: 2rem 0;
}

.welcome-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.welcome-content h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.welcome-content p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.avatar-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid rgba(255,255,255,0.3);
    object-fit: cover;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    margin: 0;
    color: #333;
}

.stat-label {
    color: #666;
    margin: 0.25rem 0;
    font-size: 0.9rem;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
}

.stat-change.positive {
    background: #d4edda;
    color: #155724;
}

.stat-change.negative {
    background: #f8d7da;
    color: #721c24;
}

.stat-change.neutral {
    background: #e2e3e5;
    color: #6c757d;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.activity-list {
    padding: 0;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #667eea;
}

.activity-content p {
    margin: 0;
    font-size: 0.9rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #666;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s;
}

.action-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.action-btn i {
    font-size: 1.5rem;
}

.action-btn span {
    font-size: 0.9rem;
    font-weight: 600;
    text-align: center;
}

.chart-section {
    margin-bottom: 2rem;
}

.chart-controls {
    display: flex;
    gap: 1rem;
}

.chart-period {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
}

.chart-container {
    height: 300px;
    padding: 1rem 0;
}

.view-all {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
}

.view-all:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .welcome-section {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{{end}}

{{define "scripts"}}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize Activity Chart
const ctx = document.getElementById('activityChart').getContext('2d');
const activityChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'CN'],
        datasets: [{
            label: 'Hoạt Động',
            data: [65, 59, 80, 81, 56, 55, 40],
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0,0,0,0.1)'
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});

// Chart period change handler
document.querySelector('.chart-period').addEventListener('change', function() {
    // Update chart data based on selected period
    // This would typically fetch new data from API
    console.log('Chart period changed to:', this.value);
});
</script>
{{end}}
