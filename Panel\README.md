Panel/
├── main.go                         # Điểm khởi động chương trình
├── go.mod                          # Thông tin module Go
├── config/
│   └── database.go                 # Kết nối SQLite
│
├── controllers/                   # Xử lý logic API
│   ├── home_controller.go
│   ├── user_controller.go
│   └── auth_controller.go
│
├── models/                        # C<PERSON>u trúc dữ liệu, kế<PERSON> nối DB
│   └── user.go
│
├── routes/                        # Khai báo route
│   └── routes.go
│
├── middleware/                    # Các middleware (auth, session...)
│   └── auth.go
│
├── static/                        # JS, CSS tĩnh (dùng cho View)
│   ├── css/
│   │   └── style.css
│   └── js/
│       └── main.js
│
├── views/                         # Giao diện frontend
│   ├── layout.html                # Giao diện chung
│
│   ├── home/                      # Giao diện người dùng
│   │   ├── index.html             # Trang chủ
│   │   └── profile.html           # Trang thông tin cá nhân
│
│   ├── chat/                      # Giao diện chat
│   │   └── chat.html
│
│   ├── admin/                     # Giao diện quản trị viên
│   │   └── dashboard.html
│
│   └── auth/                      # Đ<PERSON><PERSON> nhập, đ<PERSON><PERSON> ký, quên mật khẩu...
│       ├── login.html
│       ├── register.html
│       ├── forgot_password.html
│       └── google_login.html
│
└── README.md
